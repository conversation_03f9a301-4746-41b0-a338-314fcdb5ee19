#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - MCP路由系统配置
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# API配置
API_URL = os.getenv("API_URL", "http://************:17600/v1")
API_KEY = os.getenv("API_KEY", "sk-ngZ9das74QnIgbOH55E401EdBcF94480B8360fE94d5eC362")

# 模型配置
MODEL_NAME = os.getenv("MODEL_NAME", "claude-3-5-sonnet")
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

# 校对服务配置
SERVICES_CONFIG = {
    "legal": {
        "name": "法律法规校对",
        "description": "校对法律条文、法规、司法解释等法律相关内容"
    },
    "academic": {
        "name": "论文观点校对", 
        "description": "校对学术论文、研究报告、学术观点等内容"
    },
    "science": {
        "name": "科学百科校对",
        "description": "校对科学事实、科普内容、技术说明等科学相关内容"
    }
}
