#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - MCP路由系统配置
"""


# API配置 - 直接使用固定值，不从环境变量读取        
API_URL = "http://************:17600/v1"
API_KEY = "sk-ngZ9das74QnIgbOH55E401EdBcF94480B8360fE94d5eC362"

# 模型配置 
MODEL_NAME = "claude-3-5-sonnet"
TEMPERATURE = 0.1

# 校对服务配置      
SERVICES_CONFIG = {
    "legal": {
        "name": "法律法规校对",
        "description": "校对法律条文、法规、司法解释等法律相关内容"
    },
    "academic": {
        "name": "论文观点校对", 
        "description": "校对学术论文、研究报告、学术观点等内容"
    },
    "science": {
        "name": "科学百科校对",
        "description": "校对科学事实、科普内容、技术说明等科学相关内容"
    }
}
