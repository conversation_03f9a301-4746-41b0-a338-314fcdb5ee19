#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP路由系统 - 使用LangChain框架实现智能校对路由
"""

import json
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser


class ProofreadingService:
    """校对服务基类"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """校对方法 - 子类需要实现"""
        raise NotImplementedError


class LegalProofreadingService(ProofreadingService):
    """法律法规校对服务"""
    
    def __init__(self):
        super().__init__("法律法规校对")
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """校对法律法规内容"""
        # 模拟校对逻辑
        if "宪法" in text and "违法" in text:
            return {
                "has_error": True,
                "errors": [
                    {
                        "position": "第2句",
                        "error": "法律条文引用不准确",
                        "suggestion": "建议查阅最新的法律条文，确保引用准确性"
                    }
                ],
                "service": self.service_name
            }
        else:
            return {
                "has_error": False,
                "message": "法律法规内容校对通过，未发现错误",
                "service": self.service_name
            }


class AcademicProofreadingService(ProofreadingService):
    """论文观点校对服务"""
    
    def __init__(self):
        super().__init__("论文观点校对")
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """校对论文观点内容"""
        # 模拟校对逻辑
        if "研究表明" in text and "没有证据" in text:
            return {
                "has_error": True,
                "errors": [
                    {
                        "position": "论证部分",
                        "error": "论据不充分，缺乏可靠的研究支撑",
                        "suggestion": "建议补充更多权威研究文献，加强论证的可信度"
                    }
                ],
                "service": self.service_name
            }
        else:
            return {
                "has_error": False,
                "message": "论文观点校对通过，论证逻辑清晰",
                "service": self.service_name
            }


class ScienceProofreadingService(ProofreadingService):
    """科学百科校对服务"""
    
    def __init__(self):
        super().__init__("科学百科校对")
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """校对科学百科内容"""
        # 模拟校对逻辑
        if "地球是平的" in text or "疫苗有害" in text:
            return {
                "has_error": True,
                "errors": [
                    {
                        "position": "科学事实描述",
                        "error": "科学事实错误",
                        "suggestion": "请参考权威科学资料，更正错误的科学描述"
                    }
                ],
                "service": self.service_name
            }
        else:
            return {
                "has_error": False,
                "message": "科学百科内容校对通过，科学事实准确",
                "service": self.service_name
            }


class MCPRouter:
    """MCP路由器 - 智能路由校对请求"""
    
    def __init__(self, api_url: str, api_key: str, model_name: str = "claude-3-5-sonnet", temperature: float = 0.1):
        # 初始化LangChain聊天模型
        self.llm = ChatOpenAI(
            base_url=api_url,
            api_key=api_key,
            model=model_name,
            temperature=temperature
        )
        
        # 初始化校对服务
        self.services = {
            "legal": LegalProofreadingService(),
            "academic": AcademicProofreadingService(),
            "science": ScienceProofreadingService()
        }
        
        # 创建路由判断提示模板
        self.routing_prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能路由系统，需要分析用户提供的文本内容，判断需要哪种类型的校对服务。

可用的校对服务：
1. legal - 法律法规校对：适用于包含法律条文、法规、司法解释等法律相关内容
2. academic - 论文观点校对：适用于学术论文、研究报告、学术观点等内容
3. science - 科学百科校对：适用于科学事实、科普内容、技术说明等科学相关内容

请分析以下文本，返回需要的校对服务类型。如果需要多种服务，请都列出。
返回格式为JSON：{{"services": ["service1", "service2", ...], "reason": "选择原因"}}"""),
            ("human", "请分析以下文本需要哪种校对服务：\n\n{text}")
        ])
    
    def route_text(self, text: str) -> List[str]:
        """路由文本到合适的校对服务"""
        try:
            # 使用LLM判断需要的服务
            chain = self.routing_prompt | self.llm | StrOutputParser()
            response = chain.invoke({"text": text})
            
            # 解析响应
            result = json.loads(response)
            services = result.get("services", [])
            reason = result.get("reason", "")
            
            print(f"路由分析结果：{reason}")
            print(f"选择的服务：{services}")
            
            return services
            
        except Exception as e:
            print(f"路由分析出错：{e}")
            # 默认返回所有服务
            return ["legal", "academic", "science"]
    
    def proofread_text(self, text: str) -> Dict[str, Any]:
        """对文本进行校对"""
        print(f"开始校对文本：{text[:100]}...")
        
        # 1. 路由判断
        required_services = self.route_text(text)
        
        # 2. 执行校对
        results = {}
        for service_name in required_services:
            if service_name in self.services:
                print(f"执行 {self.services[service_name].service_name}...")
                result = self.services[service_name].proofread(text)
                results[service_name] = result
        
        # 3. 汇总结果
        final_result = self._summarize_results(results)
        return final_result
    
    def _summarize_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """汇总校对结果"""
        has_any_error = any(result.get("has_error", False) for result in results.values())
        all_errors = []
        all_messages = []
        
        for service_name, result in results.items():
            if result.get("has_error", False):
                errors = result.get("errors", [])
                for error in errors:
                    error["service"] = result.get("service", service_name)
                all_errors.extend(errors)
            else:
                all_messages.append(result.get("message", ""))
        
        return {
            "has_error": has_any_error,
            "errors": all_errors,
            "success_messages": all_messages,
            "services_used": list(results.keys()),
            "summary": f"共使用了 {len(results)} 个校对服务，发现 {len(all_errors)} 个错误"
        }