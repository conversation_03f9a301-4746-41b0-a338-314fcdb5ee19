#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP路由系统 - 使用LangChain框架实现智能校对路由
"""

import json
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser

# 导入各个校对服务
from legal_proofreading import LegalProofreadingService
from academic_proofreading import AcademicProofreadingService
from science_proofreading import ScienceProofreadingService





class MCPRouter:
    """MCP路由器 - 智能路由校对请求"""
    
    def __init__(self, api_url: str, api_key: str, model_name: str = "claude-3-5-sonnet", temperature: float = 0.1):
        # 初始化LangChain聊天模型
        self.llm = ChatOpenAI(
            base_url=api_url,
            api_key=api_key,
            model=model_name,
            temperature=temperature
        )
        
        # 初始化校对服务
        self.services = {
            "legal": LegalProofreadingService(),
            "academic": AcademicProofreadingService(),
            "science": ScienceProofreadingService()
        }
        
        # 创建路由判断提示模板
        self.routing_prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能路由系统，需要分析用户提供的文本内容，判断需要哪种类型的校对服务。

可用的校对服务：
1. legal - 法律法规校对：适用于包含法律条文、法规、司法解释等法律相关内容
2. academic - 论文观点校对：适用于学术论文、研究报告、学术观点等内容
3. science - 科学百科校对：适用于科学事实、科普内容、技术说明等科学相关内容

请分析以下文本，返回需要的校对服务类型。如果需要多种服务，请都列出。
返回格式为JSON：{{"services": ["service1", "service2", ...], "reason": "选择原因"}}"""),
            ("human", "请分析以下文本需要哪种校对服务：\n\n{text}")
        ])
    
    def route_text(self, text: str) -> List[str]:
        """路由文本到合适的校对服务"""
        try:
            # 使用LLM判断需要的服务
            chain = self.routing_prompt | self.llm | StrOutputParser()

            # 打印最终输入给大模型的内容
            formatted_prompt = self.routing_prompt.format(text=text)
            print(f"🔍 最终输入给大模型的提示内容：")
            print(f"{'='*60}")
            print(formatted_prompt)
            print(f"{'='*60}")

            response = chain.invoke({"text": text})

            print(f"🤖 LLM原始响应：")
            print(f"{'='*60}")
            print(repr(response))  # 使用repr显示所有字符，包括控制字符
            print(f"{'='*60}")

            # 清理响应内容
            cleaned_response = response.strip()
            # 移除可能的控制字符
            import re
            cleaned_response = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', cleaned_response)

            print(f"🧹 清理后的响应：")
            print(f"{'='*60}")
            print(cleaned_response)
            print(f"{'='*60}")

            # 解析响应
            try:
                result = json.loads(cleaned_response)
                print(f"格式化之后的json==={result}")
                services = result.get("services", [])
                reason = result.get("reason", "")

                print(f"路由分析结果：{reason}")
                print(f"选择的服务：{services}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误：{e}")
                print(f"尝试备用解析方法...")
                # 备用解析：尝试提取services数组
                services = self._extract_services_fallback(cleaned_response)
                reason = "JSON解析失败，使用备用方法"
                print(f"路由分析结果：{reason}")
                print(f"选择的服务：{services}")
            
            return services
            
        except Exception as e:
            print(f"路由分析出错：{e}")
            # 默认返回所有服务
            return ["legal", "academic", "science"]

    def _extract_services_fallback(self, response: str) -> List[str]:
        """备用服务提取方法"""
        import re

        # 尝试提取 ["service1", "service2"] 格式
        services_match = re.search(r'\["([^"]+)"(?:,\s*"([^"]+)")*\]', response)
        if services_match:
            services = [services_match.group(1)]
            if services_match.group(2):
                services.append(services_match.group(2))
            return services

        # 关键词匹配备用方案
        services = []
        if "legal" in response.lower() or "法律" in response:
            services.append("legal")
        if "academic" in response.lower() or "学术" in response or "论文" in response:
            services.append("academic")
        if "science" in response.lower() or "科学" in response:
            services.append("science")

        return services if services else ["academic"]

    def proofread_text(self, text: str) -> Dict[str, Any]:
        """对文本进行校对"""
        print(f"开始校对文本：{text[:100]}...")
        
        # 1. 路由判断
        required_services = self.route_text(text)
        
        # 2. 执行校对
        results = {}
        for service_name in required_services:
            if service_name in self.services:
                print(f"执行 {self.services[service_name].service_name}...")
                result = self.services[service_name].proofread(text)
                results[service_name] = result
        
        # 3. 汇总结果
        final_result = self._summarize_results(results)
        return final_result
    
    def _summarize_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """汇总校对结果"""
        has_any_error = any(result.get("has_error", False) for result in results.values())
        all_errors = []
        all_messages = []
        
        for service_name, result in results.items():
            if result.get("has_error", False):
                errors = result.get("errors", [])
                for error in errors:
                    error["service"] = result.get("service", service_name)
                all_errors.extend(errors)
            else:
                all_messages.append(result.get("message", ""))
        
        return {
            "has_error": has_any_error,
            "errors": all_errors,
            "success_messages": all_messages,
            "services_used": list(results.keys()),
            "summary": f"共使用了 {len(results)} 个校对服务，发现 {len(all_errors)} 个错误"
        }