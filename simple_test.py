#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实LLM交互测试程序 - 使用真实API进行MCP路由功能测试
"""

import json
import requests
from typing import List, Dict, Any
from config import API_URL, API_KEY, MODEL_NAME, TEMPERATURE


class RealLLM:
    """真实LLM交互类"""

    def __init__(self, api_url: str, api_key: str, model_name: str, temperature: float = 0.1):
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.temperature = temperature

        # 路由判断的系统提示
        self.system_prompt = """你是一个智能路由系统，需要分析用户提供的文本内容，判断需要哪种类型的校对服务。

可用的校对服务：
1. legal - 法律法规校对：适用于包含法律条文、法规、司法解释等法律相关内容
2. academic - 论文观点校对：适用于学术论文、研究报告、学术观点等内容
3. science - 科学百科校对：适用于科学事实、科普内容、技术说明等科学相关内容

请分析以下文本，返回需要的校对服务类型。如果需要多种服务，请都列出。
返回格式必须是JSON：{"services": ["service1", "service2", ...], "reason": "选择原因"}

注意：
- 只返回JSON格式，不要其他解释
- services数组中只能包含：legal, academic, science
- 至少选择一个服务"""

    def route_text(self, text: str) -> List[str]:
        """使用真实LLM进行路由判断"""
        try:
            # 构建请求数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"请分析以下文本需要哪种校对服务：\n\n{text}"}
                ],
                "temperature": self.temperature,
                "max_tokens": 500
            }

            print(f"正在调用LLM进行路由分析...")
            print(f"API URL: {self.api_url}/chat/completions")
            print(f"模型: {self.model_name}")

            # 发送请求
            response = requests.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                print(f"LLM响应: {content}")

                # 解析JSON响应
                try:
                    parsed_result = json.loads(content)
                    services = parsed_result.get("services", [])
                    reason = parsed_result.get("reason", "")

                    print(f"路由分析结果：{reason}")
                    print(f"选择的服务：{services}")

                    return services

                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"原始响应: {content}")
                    # 尝试从响应中提取服务类型
                    return self._fallback_parse(content)

            else:
                print(f"API请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return self._fallback_route(text)

        except Exception as e:
            print(f"LLM调用出错：{e}")
            return self._fallback_route(text)

    def _fallback_parse(self, content: str) -> List[str]:
        """备用解析方法"""
        services = []
        if "legal" in content.lower():
            services.append("legal")
        if "academic" in content.lower():
            services.append("academic")
        if "science" in content.lower():
            services.append("science")

        return services if services else ["academic"]

    def _fallback_route(self, text: str) -> List[str]:
        """备用路由方法（关键词匹配）"""
        print("使用备用路由方法（关键词匹配）")
        services = []

        legal_keywords = ["法律", "宪法", "条例", "法规", "司法", "违法", "合法"]
        academic_keywords = ["研究", "论文", "学术", "实验", "数据", "分析", "理论"]
        science_keywords = ["科学", "技术", "实验", "化学", "物理", "生物", "地球", "疫苗"]

        if any(keyword in text for keyword in legal_keywords):
            services.append("legal")

        if any(keyword in text for keyword in academic_keywords):
            services.append("academic")

        if any(keyword in text for keyword in science_keywords):
            services.append("science")

        return services if services else ["academic"]


class SimpleProofreadingService:
    """简化的校对服务"""
    
    def __init__(self, service_name: str, error_keywords: List[str]):
        self.service_name = service_name
        self.error_keywords = error_keywords
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """执行校对"""
        errors = []
        
        for keyword in self.error_keywords:
            if keyword in text:
                errors.append({
                    "position": f"包含'{keyword}'的部分",
                    "error": f"发现可能的错误内容：{keyword}",
                    "suggestion": f"建议检查和修正关于'{keyword}'的描述"
                })
        
        if errors:
            return {
                "has_error": True,
                "errors": errors,
                "service": self.service_name
            }
        else:
            return {
                "has_error": False,
                "message": f"{self.service_name}校对通过，未发现明显错误",
                "service": self.service_name
            }


class RealMCPRouter:
    """真实LLM的MCP路由器"""

    def __init__(self):
        # 初始化真实LLM
        self.llm = RealLLM(API_URL, API_KEY, MODEL_NAME, TEMPERATURE)

        # 初始化校对服务
        self.services = {
            "legal": SimpleProofreadingService(
                "法律法规校对",
                ["违法", "非法", "错误条文"]
            ),
            "academic": SimpleProofreadingService(
                "论文观点校对",
                ["没有证据", "缺乏依据", "无根据"]
            ),
            "science": SimpleProofreadingService(
                "科学百科校对",
                ["地球是平的", "疫苗有害", "伪科学"]
            )
        }
    
    def proofread_text(self, text: str) -> Dict[str, Any]:
        """对文本进行校对"""
        print(f"开始校对文本：{text}")
        print("-" * 50)
        
        # 1. 路由判断
        required_services = self.llm.route_text(text)
        print(f"路由结果：需要使用服务 {required_services}")
        
        # 2. 执行校对
        results = {}
        for service_name in required_services:
            if service_name in self.services:
                print(f"正在执行 {self.services[service_name].service_name}...")
                result = self.services[service_name].proofread(text)
                results[service_name] = result
        
        # 3. 汇总结果
        final_result = self._summarize_results(results)
        return final_result
    
    def _summarize_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """汇总校对结果"""
        has_any_error = any(result.get("has_error", False) for result in results.values())
        all_errors = []
        all_messages = []
        
        for service_name, result in results.items():
            if result.get("has_error", False):
                errors = result.get("errors", [])
                for error in errors:
                    error["service"] = result.get("service", service_name)
                all_errors.extend(errors)
            else:
                all_messages.append(result.get("message", ""))
        
        return {
            "has_error": has_any_error,
            "errors": all_errors,
            "success_messages": all_messages,
            "services_used": list(results.keys()),
            "summary": f"共使用了 {len(results)} 个校对服务，发现 {len(all_errors)} 个错误"
        }


def main():
    """主函数 - 真实LLM测试"""

    print("=== MCP智能校对路由系统 - 真实LLM测试 ===\n")
    print(f"API配置: {API_URL}")
    print(f"模型: {MODEL_NAME}")
    print("-" * 50)

    # 创建路由器
    router = RealMCPRouter()
    
    # 测试文本
    test_texts = [
        "根据宪法规定，公民有言论自由的权利，但这种权利是违法的。",
        "我们的研究表明，这种新药物非常有效，尽管没有证据支持这一结论。", 
        "地球是平的，这是一个科学事实，疫苗对人体有害。",
        "本研究基于大量实验数据，证明了新材料在高温环境下的优异性能。"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}")
        print(f"{'='*60}")
        
        result = router.proofread_text(text)
        
        print(f"\n校对结果总结：")
        print(f"发现错误：{'是' if result['has_error'] else '否'}")
        print(f"使用的服务：{', '.join(result['services_used'])}")
        print(f"结果概述：{result['summary']}")
        
        if result['has_error']:
            print(f"\n发现的错误：")
            for j, error in enumerate(result['errors'], 1):
                print(f"  错误 {j}：")
                print(f"    位置：{error['position']}")
                print(f"    问题：{error['error']}")
                print(f"    建议：{error['suggestion']}")
                print(f"    校对服务：{error['service']}")
        
        if result['success_messages']:
            print(f"\n通过的校对：")
            for msg in result['success_messages']:
                print(f"  ✓ {msg}")
    
    print(f"\n{'='*60}")
    print("测试完成！")
    
    # 交互式测试
    print(f"\n您也可以输入自己的文本进行测试：")
    while True:
        user_input = input("\n请输入要校对的文本（输入'quit'退出）：")
        if user_input.lower() == 'quit':
            break
        
        if user_input.strip():
            print(f"\n{'='*40}")
            result = router.proofread_text(user_input)
            
            print(f"\n校对结果：")
            print(f"发现错误：{'是' if result['has_error'] else '否'}")
            print(f"使用的服务：{', '.join(result['services_used'])}")
            
            if result['has_error']:
                print(f"\n错误详情：")
                for error in result['errors']:
                    print(f"  - {error['error']} ({error['service']})")
                    print(f"    建议：{error['suggestion']}")
            else:
                print(f"\n✓ 校对通过，未发现明显错误")


if __name__ == "__main__":
    main()
