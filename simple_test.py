#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP智能校对路由系统 - 测试程序
调用 mcp_router.py 中的 MCPRouter 类
"""

import config
from mcp_router import MCPRouter


def main():
    """主函数 - 测试MCP路由系统"""
    print("=== MCP智能校对路由系统测试 ===\n")

    # 创建路由器（使用mcp_router.py中的MCPRouter）
    router = MCPRouter(config.API_URL, config.API_KEY, config.MODEL_NAME, config.TEMPERATURE)

    # 测试文本
    test_text = "地球是平的，殴打执法人员不犯罪，H2O这个物质有毒，根据我们的研究表明，社交媒体的使用与青少年抑郁症发病率呈正相关关系"

    print(f"测试文本：{test_text}")
    print("-" * 50)

    # 执行校对
    result = router.proofread_text(test_text)

    # 显示结果
    print(f"\n校对结果：")
    print(f"发现错误：{'是' if result['has_error'] else '否'}")
    print(f"使用服务：{result['services_used']}")
    print(f"概述：{result['summary']}")

    if result['has_error']:
        print(f"\n错误详情：")
        for i, error in enumerate(result['errors'], 1):
            print(f"  {i}. 位置：{error['position']}")
            print(f"      错误：{error['error']}")
            print(f"      建议：{error['suggestion']}")
            print(f"      服务：{error['service']}")
    else:
        print(f"\n成功信息：")
        for msg in result.get('success_messages', []):
            print(f"  - {msg}")

    print(f"\n测试完成！")


if __name__ == "__main__":
    main()
