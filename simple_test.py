#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP智能校对路由系统 - 简化版本
"""

import json
import requests
from typing import List, Dict, Any
import config


class LLMRouter:
    """LLM路由器"""

    def __init__(self):
        self.api_url = config.API_URL
        self.api_key = config.API_KEY
        self.model_name = config.MODEL_NAME
        self.temperature = config.TEMPERATURE

        self.system_prompt = """你是一个智能路由系统，分析文本内容，判断需要哪种校对服务。

可用服务：
1. legal - 法律法规校对
2. academic - 论文观点校对
3. science - 科学百科校对

返回JSON格式：{"services": ["service1", "service2"], "reason": "选择原因"}"""

    def route_text(self, text: str) -> List[str]:
        """路由文本到合适的校对服务"""
        try:
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {self.api_key}"}
            data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"分析文本：{text}"}
                ],
                "temperature": self.temperature,
                "max_tokens": 300
            }

            print("正在调用LLM...")
            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                content = response.json()['choices'][0]['message']['content'].strip()
                print(f"LLM响应: {content}")

                parsed_result = json.loads(content)
                services = parsed_result.get("services", [])
                reason = parsed_result.get("reason", "")
                print(f"路由结果：{reason}")
                return services
            else:
                print(f"API失败: {response.status_code}")
                return self._fallback_route(text)

        except Exception as e:
            print(f"LLM调用出错：{e}")
            return self._fallback_route(text)

    def _fallback_route(self, text: str) -> List[str]:
        """备用路由（关键词匹配）"""
        print("使用关键词匹配路由")
        services = []

        if any(word in text for word in ["法律", "宪法", "条例", "违法"]):
            services.append("legal")
        if any(word in text for word in ["研究", "论文", "学术", "数据"]):
            services.append("academic")
        if any(word in text for word in ["科学", "技术", "地球", "疫苗"]):
            services.append("science")

        return services if services else ["academic"]


class ProofreadingService:
    """校对服务"""

    def __init__(self, name: str, error_keywords: List[str]):
        self.name = name
        self.error_keywords = error_keywords

    def proofread(self, text: str) -> Dict[str, Any]:
        """执行校对"""
        errors = []
        for keyword in self.error_keywords:
            if keyword in text:
                errors.append({
                    "error": f"发现错误：{keyword}",
                    "suggestion": f"建议修正关于'{keyword}'的描述"
                })

        return {
            "has_error": bool(errors),
            "errors": errors,
            "service": self.name
        }


class SimpleMCPRouter:
    """简化版MCP路由器"""

    def __init__(self):
        self.llm_router = LLMRouter()
        self.services = {
            "legal": ProofreadingService("法律法规校对", ["违法", "非法"]),
            "academic": ProofreadingService("论文观点校对", ["没有证据", "缺乏依据"]),
            "science": ProofreadingService("科学百科校对", ["地球是平的", "疫苗有害"])
        }

    def proofread_text(self, text: str) -> Dict[str, Any]:
        """校对文本"""
        print(f"开始校对：{text}")
        print("-" * 40)

        # 路由判断
        required_services = self.llm_router.route_text(text)
        print(f"选择服务：{required_services}")

        # 执行校对
        results = {}
        for service_name in required_services:
            if service_name in self.services:
                print(f"执行 {self.services[service_name].name}...")
                result = self.services[service_name].proofread(text)
                results[service_name] = result

        # 汇总结果
        all_errors = []
        for result in results.values():
            if result.get("has_error"):
                all_errors.extend(result.get("errors", []))

        return {
            "has_error": bool(all_errors),
            "errors": all_errors,
            "services_used": list(results.keys()),
            "summary": f"使用了 {len(results)} 个服务，发现 {len(all_errors)} 个错误"
        }


def main():
    """主函数"""
    print("=== MCP智能校对路由系统测试 ===\n")

    # 创建路由器
    router = SimpleMCPRouter()

    # 测试文本
    test_text = "根据宪法规定，公民有言论自由的权利，但这种权利是违法的。"

    # 执行校对
    result = router.proofread_text(test_text)

    # 显示结果
    print(f"\n校对结果：")
    print(f"发现错误：{'是' if result['has_error'] else '否'}")
    print(f"使用服务：{result['services_used']}")
    print(f"概述：{result['summary']}")

    if result['has_error']:
        print(f"\n错误详情：")
        for i, error in enumerate(result['errors'], 1):
            print(f"  {i}. {error['error']}")
            print(f"     {error['suggestion']}")

    print(f"\n测试完成！")


if __name__ == "__main__":
    main()
