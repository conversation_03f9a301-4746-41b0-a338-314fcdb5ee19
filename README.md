# MCP智能校对路由系统

基于LangChain框架实现的智能校对路由系统，能够自动判断文本类型并路由到相应的校对服务。

## 功能特点

- 🤖 **智能路由**：使用大模型自动判断文本需要哪种校对服务
- 📚 **多种校对**：支持法律法规、论文观点、科学百科三种校对类型
- 🔄 **统一接口**：所有校对服务使用统一的接口模式
- 📊 **结果汇总**：自动汇总多个校对服务的结果

## 校对服务类型

1. **法律法规校对** - 校对法律条文、法规、司法解释等法律相关内容
2. **论文观点校对** - 校对学术论文、研究报告、学术观点等内容  
3. **科学百科校对** - 校对科学事实、科普内容、技术说明等科学相关内容

## 文件说明

- `mcp_router.py` - 完整版本，需要配置真实的API
- `simple_test.py` - 简化版本，使用模拟LLM，可直接运行测试
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
- `.env.example` - 环境变量配置示例

## 快速开始

### 方法1：简单测试（推荐）

直接运行简化版本，无需配置API：

```bash
python simple_test.py
```

### 方法2：完整版本

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置API：
```bash
# 复制配置文件
cp .env.example .env

# 编辑.env文件，填入您的API信息
API_URL=https://your-api-url.com/v1
API_KEY=your-api-key-here
```

3. 运行完整版本：
```bash
python mcp_router.py
```

## 使用示例

```python
from simple_test import SimpleMCPRouter

# 创建路由器
router = SimpleMCPRouter()

# 校对文本
text = "根据宪法规定，公民有言论自由的权利，但这种权利是违法的。"
result = router.proofread_text(text)

# 查看结果
print(f"发现错误：{result['has_error']}")
print(f"使用服务：{result['services_used']}")
if result['has_error']:
    for error in result['errors']:
        print(f"错误：{error['error']}")
        print(f"建议：{error['suggestion']}")
```

## 测试文本示例

系统包含以下测试文本：

1. **法律文本**：`"根据宪法规定，公民有言论自由的权利，但这种权利是违法的。"`
2. **学术文本**：`"我们的研究表明，这种新药物非常有效，尽管没有证据支持这一结论。"`
3. **科学文本**：`"地球是平的，这是一个科学事实，疫苗对人体有害。"`
4. **正常文本**：`"本研究基于大量实验数据，证明了新材料在高温环境下的优异性能。"`

## 系统架构

```
用户输入文本
    ↓
智能路由判断 (LLM)
    ↓
选择校对服务
    ↓
并行执行校对
    ↓
汇总校对结果
    ↓
返回最终结果
```

## 扩展说明

- 可以轻松添加新的校对服务类型
- 支持自定义校对逻辑
- 可以集成真实的校对API或服务
- 支持配置不同的大模型进行路由判断

## 注意事项

- 简化版本使用关键词匹配进行路由判断
- 完整版本需要配置真实的大模型API
- 校对逻辑目前是模拟实现，可根据需要替换为真实的校对服务
