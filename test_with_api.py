#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动配置API的测试程序
"""

import json
import requests
from typing import List, Dict, Any


class RealLLM:
    """真实LLM交互类"""
    
    def __init__(self, api_url: str, api_key: str, model_name: str = "gpt-3.5-turbo"):
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        
        # 路由判断的系统提示
        self.system_prompt = """你是一个智能路由系统，需要分析用户提供的文本内容，判断需要哪种类型的校对服务。

可用的校对服务：
1. legal - 法律法规校对：适用于包含法律条文、法规、司法解释等法律相关内容
2. academic - 论文观点校对：适用于学术论文、研究报告、学术观点等内容
3. science - 科学百科校对：适用于科学事实、科普内容、技术说明等科学相关内容

请分析以下文本，返回需要的校对服务类型。如果需要多种服务，请都列出。
返回格式必须是JSON：{"services": ["service1", "service2", ...], "reason": "选择原因"}

注意：
- 只返回JSON格式，不要其他解释
- services数组中只能包含：legal, academic, science
- 至少选择一个服务"""
    
    def route_text(self, text: str) -> List[str]:
        """使用真实LLM进行路由判断"""
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"请分析以下文本需要哪种校对服务：\n\n{text}"}
                ],
                "temperature": 0.1,
                "max_tokens": 500
            }
            
            print(f"正在调用LLM...")
            
            response = requests.post(
                f"{self.api_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                print(f"LLM响应: {content}")
                
                try:
                    parsed_result = json.loads(content)
                    services = parsed_result.get("services", [])
                    reason = parsed_result.get("reason", "")
                    
                    print(f"路由分析结果：{reason}")
                    return services
                    
                except json.JSONDecodeError:
                    print("JSON解析失败，使用备用方法")
                    return self._fallback_route(text)
                    
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
                return self._fallback_route(text)
                
        except Exception as e:
            print(f"LLM调用出错：{e}")
            return self._fallback_route(text)
    
    def _fallback_route(self, text: str) -> List[str]:
        """备用路由方法"""
        print("使用关键词匹配进行路由")
        services = []
        
        if any(word in text for word in ["法律", "宪法", "条例", "法规", "违法"]):
            services.append("legal")
        if any(word in text for word in ["研究", "论文", "学术", "实验", "数据"]):
            services.append("academic")
        if any(word in text for word in ["科学", "技术", "地球", "疫苗", "化学"]):
            services.append("science")
        
        return services if services else ["academic"]


class SimpleProofreadingService:
    """简化的校对服务"""
    
    def __init__(self, service_name: str, error_keywords: List[str]):
        self.service_name = service_name
        self.error_keywords = error_keywords
    
    def proofread(self, text: str) -> Dict[str, Any]:
        """执行校对"""
        errors = []
        
        for keyword in self.error_keywords:
            if keyword in text:
                errors.append({
                    "position": f"包含'{keyword}'的部分",
                    "error": f"发现可能的错误内容：{keyword}",
                    "suggestion": f"建议检查和修正关于'{keyword}'的描述"
                })
        
        if errors:
            return {
                "has_error": True,
                "errors": errors,
                "service": self.service_name
            }
        else:
            return {
                "has_error": False,
                "message": f"{self.service_name}校对通过，未发现明显错误",
                "service": self.service_name
            }


class MCPRouter:
    """MCP路由器"""
    
    def __init__(self, api_url: str, api_key: str, model_name: str = "gpt-3.5-turbo"):
        self.llm = RealLLM(api_url, api_key, model_name)
        
        self.services = {
            "legal": SimpleProofreadingService("法律法规校对", ["违法", "非法"]),
            "academic": SimpleProofreadingService("论文观点校对", ["没有证据", "缺乏依据"]),
            "science": SimpleProofreadingService("科学百科校对", ["地球是平的", "疫苗有害"])
        }
    
    def proofread_text(self, text: str) -> Dict[str, Any]:
        """校对文本"""
        print(f"开始校对：{text}")
        print("-" * 40)
        
        # 路由判断
        required_services = self.llm.route_text(text)
        print(f"选择的服务：{required_services}")
        
        # 执行校对
        results = {}
        for service_name in required_services:
            if service_name in self.services:
                print(f"执行 {self.services[service_name].service_name}...")
                result = self.services[service_name].proofread(text)
                results[service_name] = result
        
        # 汇总结果
        return self._summarize_results(results)
    
    def _summarize_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """汇总结果"""
        has_any_error = any(result.get("has_error", False) for result in results.values())
        all_errors = []
        all_messages = []
        
        for result in results.values():
            if result.get("has_error", False):
                all_errors.extend(result.get("errors", []))
            else:
                all_messages.append(result.get("message", ""))
        
        return {
            "has_error": has_any_error,
            "errors": all_errors,
            "success_messages": all_messages,
            "services_used": list(results.keys()),
            "summary": f"使用了 {len(results)} 个服务，发现 {len(all_errors)} 个错误"
        }


def main():
    """主函数"""
    print("=== MCP智能校对路由系统测试 ===\n")
    
    # 手动配置API信息
    api_url = input("请输入API URL (默认: http://************:17600/v1): ").strip()
    if not api_url:
        api_url = "http://************:17600/v1"
    
    api_key = input("请输入API Key: ").strip()
    if not api_key:
        print("未提供API Key，将使用备用路由方法")
        api_key = "dummy"
    
    model_name = input("请输入模型名称 (默认: claude-3-5-sonnet): ").strip()
    if not model_name:
        model_name = "claude-3-5-sonnet"
    
    print(f"\n配置信息：")
    print(f"API URL: {api_url}")
    print(f"模型: {model_name}")
    print("-" * 50)
    
    # 创建路由器
    router = MCPRouter(api_url, api_key, model_name)
    
    # 测试文本
    test_text = "根据宪法规定，公民有言论自由的权利，但这种权利是违法的。"
    
    # 执行校对
    result = router.proofread_text(test_text)
    
    # 显示结果
    print(f"\n最终结果：")
    print(f"发现错误：{'是' if result['has_error'] else '否'}")
    print(f"使用的服务：{', '.join(result['services_used'])}")
    print(f"概述：{result['summary']}")
    
    if result['has_error']:
        print(f"\n错误详情：")
        for i, error in enumerate(result['errors'], 1):
            print(f"  {i}. {error['error']}")
            print(f"     建议：{error['suggestion']}")
    
    if result['success_messages']:
        print(f"\n成功信息：")
        for msg in result['success_messages']:
            print(f"  ✓ {msg}")


if __name__ == "__main__":
    main()
